'use strict';

/**
 * reCAPTCHA Audio Solver v3.6.1 - Enhanced
 * Background script with improved performance and reliability
 */

// Configuration constants
const CONFIG = {
  VERSION: 'v3.6.1',
  BADGE_COLORS: {
    ACTIVE: '#4CAF50',
    INACTIVE: '#EA4335',
    PROCESSING: '#FBBC05'
  },
  COOKIE_BATCH_SIZE: 10,
  REQUEST_TIMEOUT: 30000,
  MAX_RETRIES: 3
};

// Initialize extension state with enhanced error handling
chrome.runtime.onInstalled.addListener(async () => {
  try {
    await chrome.storage.local.set({
      enabled: true,
      solveCount: 0,
      failCount: 0,
      lastStatus: "Aktif",
      botDetected: false,
      lastUpdated: Date.now(),
      version: CONFIG.VERSION
    });

    console.log(`reCAPTCHA Audio Solver ${CONFIG.VERSION} extension installed`);

    // Set initial badge color
    await chrome.action.setBadgeBackgroundColor({ color: CONFIG.BADGE_COLORS.ACTIVE });
  } catch (error) {
    console.error('Failed to initialize extension:', error);
    // Fallback initialization
    chrome.storage.local.set({
      enabled: true,
      solveCount: 0,
      failCount: 0,
      lastStatus: "Aktif",
      botDetected: false,
      lastUpdated: Date.now()
    });
    chrome.action.setBadgeBackgroundColor({ color: CONFIG.BADGE_COLORS.ACTIVE });
  }
});

// Enhanced cookie deletion with better performance and error handling
async function deleteCookiesForRecaptcha() {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  console.log(`${logPrefix} Deleting cookies for recaptcha domains`);

  try {
    // Define domains to clean up
    const domains = [
      '.recaptcha.net',
      'www.recaptcha.net',
      'recaptcha.net',
      '.google.com',
      'www.google.com'
    ];

    // Get all cookies in parallel with timeout protection
    const cookiePromises = domains.map(domain =>
      Promise.race([
        chrome.cookies.getAll({ domain }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error(`Timeout for domain ${domain}`)), 5000)
        )
      ]).catch(error => {
        console.warn(`${logPrefix} Failed to get cookies for ${domain}:`, error.message);
        return [];
      })
    );

    const cookieArrays = await Promise.all(cookiePromises);
    const allCookies = cookieArrays.flat();

    // Filter Google cookies to only include reCAPTCHA-related ones
    const filteredCookies = allCookies.filter(cookie => {
      if (cookie.domain.includes('recaptcha')) return true;
      if (cookie.domain.includes('google')) {
        return cookie.name.toLowerCase().includes('captcha') ||
               cookie.path.toLowerCase().includes('recaptcha') ||
               cookie.name.toLowerCase().includes('nid') ||
               cookie.name.toLowerCase().includes('1p_jar');
      }
      return false;
    });

    // Remove duplicates based on domain + name + path
    const uniqueCookies = filteredCookies.filter((cookie, index, arr) =>
      arr.findIndex(c =>
        c.domain === cookie.domain &&
        c.name === cookie.name &&
        c.path === cookie.path
      ) === index
    );

    if (uniqueCookies.length === 0) {
      return { success: true, count: 0 };
    }

    // Delete cookies in optimized batches
    const batchSize = Math.min(CONFIG.COOKIE_BATCH_SIZE, uniqueCookies.length);
    let deletedCount = 0;

    for (let i = 0; i < uniqueCookies.length; i += batchSize) {
      const batch = uniqueCookies.slice(i, i + batchSize);

      const deletePromises = batch.map(cookie => {
        const url = `https://${cookie.domain.startsWith('.') ? cookie.domain.slice(1) : cookie.domain}${cookie.path}`;
        return chrome.cookies.remove({
          url,
          name: cookie.name
        }).then(result => {
          if (result) deletedCount++;
          return result;
        }).catch(error => {
          console.warn(`${logPrefix} Failed to delete cookie ${cookie.name}:`, error.message);
          return null;
        });
      });

      await Promise.all(deletePromises);
    }

    console.log(`${logPrefix} Successfully deleted ${deletedCount}/${uniqueCookies.length} cookies`);
    return { success: true, count: deletedCount };

  } catch (error) {
    console.error(`${logPrefix} Error deleting cookies:`, error);
    return { success: false, error: error.message };
  }
}

// Enhanced audio processing with better performance and reliability
async function processAudioFromServer(audioUrl, serverUrl) {
  const logPrefix = '[reCAPTCHA Audio Solver]';
  console.log(`${logPrefix} Processing audio with server: ${new URL(serverUrl).hostname}`);

  try {
    // Enhanced user agents with more recent versions
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ];

    // Get a random user agent
    const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];

    // Enhanced request headers
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': 'text/plain, */*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'User-Agent': userAgent,
      'Origin': chrome.runtime.getURL(''),
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'DNT': '1',
      'Sec-Fetch-Dest': 'empty',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Site': 'cross-site'
    };

    // Create AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

    try {
      // Make the request with timeout protection
      const response = await fetch(serverUrl, {
        method: 'POST',
        headers,
        body: `input=${encodeURIComponent(audioUrl)}&lang=en`,
        credentials: 'omit',
        mode: 'cors',
        cache: 'no-store',
        redirect: 'follow',
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      // Handle non-OK responses
      if (!response.ok) {
        const errorMsg = `Server error: ${response.status} ${response.statusText}`;
        console.error(`${logPrefix} ${errorMsg}`);
        return {
          success: false,
          error: errorMsg,
          status: response.status
        };
      }

      const text = await response.text();
      const trimmedText = text.trim();

      // Enhanced validation
      if (!trimmedText) {
        console.error(`${logPrefix} Empty response from server`);
        return {
          success: false,
          error: 'Empty response from server'
        };
      }

      // Check for invalid patterns
      const invalidPatterns = [/<|>/, /script/i, /javascript/i, /error/i, /fail/i];
      const hasInvalidPattern = invalidPatterns.some(pattern => pattern.test(trimmedText));

      if (hasInvalidPattern) {
        console.error(`${logPrefix} Invalid response pattern detected`);
        return {
          success: false,
          error: 'Invalid response pattern'
        };
      }

      // Enhanced length validation - allow single characters but check for specific invalid responses
      if (trimmedText.length === 0 || trimmedText.length > 50) {
        console.error(`${logPrefix} Response length invalid: ${trimmedText.length}`);
        return {
          success: false,
          error: 'Response length invalid'
        };
      }

      // Check for known invalid single-character responses
      if (trimmedText.length === 1) {
        const invalidSingleChars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        if (invalidSingleChars.includes(trimmedText)) {
          console.error(`${logPrefix} Invalid single character response: ${trimmedText}`);
          return {
            success: false,
            error: 'Invalid single character response'
          };
        }
      }

      // For responses shorter than 3 characters, add additional validation
      if (trimmedText.length < 3) {
        // Allow single letters but not numbers or special characters
        if (!/^[a-zA-Z]+$/.test(trimmedText)) {
          console.error(`${logPrefix} Invalid short response format: ${trimmedText}`);
          return {
            success: false,
            error: 'Invalid short response format'
          };
        }
      }

      // Success
      console.log(`${logPrefix} Successfully processed audio: "${trimmedText}" (length: ${trimmedText.length})`);
      return {
        success: true,
        text: trimmedText
      };

    } finally {
      clearTimeout(timeoutId);
    }

  } catch (error) {
    if (error.name === 'AbortError') {
      console.error(`${logPrefix} Request timeout`);
      return {
        success: false,
        error: 'Request timeout',
        errorType: 'Timeout'
      };
    }

    console.error(`${logPrefix} Error processing audio:`, error.message);
    return {
      success: false,
      error: error.message || 'Unknown error',
      errorType: error.name || 'Error'
    };
  }
}

// Enhanced message listener with better error handling and performance
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Validate request structure
  if (!request || typeof request !== 'object' || !request.action) {
    safelySendResponse(sendResponse, { success: false, error: 'Invalid request format' });
    return false;
  }

  const logPrefix = '[reCAPTCHA Audio Solver]';

  // Check if sender is valid (not from an invalidated context)
  if (!sender || !sender.tab) {
    console.warn(`${logPrefix} Message from invalid sender context`);
    safelySendResponse(sendResponse, { success: false, error: 'Invalid sender context' });
    return false;
  }

  try {
    switch (request.action) {
      case "updateStatus":
        handleStatusUpdate(request);
        return false; // No response needed

      case "deleteCookies":
        handleDeleteCookies(sendResponse);
        return true; // Async response

      case "processAudio":
        handleProcessAudio(request, sendResponse);
        return true; // Async response

      default:
        console.warn(`${logPrefix} Unknown action: ${request.action}`);
        safelySendResponse(sendResponse, { success: false, error: 'Unknown action' });
        return false;
    }
  } catch (error) {
    console.error(`${logPrefix} Error handling message:`, error);
    safelySendResponse(sendResponse, { success: false, error: error.message });
    return false;
  }
});

// Handle status updates with enhanced badge management
async function handleStatusUpdate(request) {
  try {
    if (request.stats) {
      // Update badge text for solved count
      if (request.stats.solved > 0) {
        await chrome.action.setBadgeText({ text: request.stats.solved.toString() });
      } else {
        await chrome.action.setBadgeText({ text: '' });
      }

      // Update badge color based on state
      let badgeColor = CONFIG.BADGE_COLORS.INACTIVE;
      if (request.stats.enabled) {
        badgeColor = request.status === 'processing' ?
          CONFIG.BADGE_COLORS.PROCESSING : CONFIG.BADGE_COLORS.ACTIVE;
      }

      await chrome.action.setBadgeBackgroundColor({ color: badgeColor });

      // Update storage with latest stats
      await chrome.storage.local.set({
        solveCount: request.stats.solved || 0,
        failCount: request.stats.failed || 0,
        lastStatus: request.status,
        lastUpdated: Date.now()
      });
    }
  } catch (error) {
    console.error('[reCAPTCHA Audio Solver] Error updating status:', error);
  }
}

// Helper function to safely send response with better error handling
const safelySendResponse = (sendResponseFunc, response) => {
  try {
    if (chrome.runtime.lastError) {
      const errorMsg = chrome.runtime.lastError.message;
      // Ignore common disconnection errors
      if (errorMsg !== "The message port closed before a response was received." &&
          errorMsg !== "A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received" &&
          errorMsg !== "Extension context invalidated." &&
          !errorMsg.includes("receiving end does not exist")) {
        console.warn(`[reCAPTCHA Audio Solver] Runtime error: ${errorMsg}`);
      }
      return false;
    }

    if (typeof sendResponseFunc === 'function') {
      try {
        sendResponseFunc(response);
        return true;
      } catch (error) {
        // Handle cases where the message port is closed
        if (error.message && (error.message.includes('port closed') ||
                             error.message.includes('Extension context invalidated'))) {
          console.log('[reCAPTCHA Audio Solver] Message port closed, ignoring response');
          return false;
        }
        throw error;
      }
    } else {
      console.error('[reCAPTCHA Audio Solver] sendResponse is not a function');
      return false;
    }
  } catch (error) {
    console.error(`[reCAPTCHA Audio Solver] Error sending response:`, error.message);
    return false;
  }
};

// Handle delete cookies request with enhanced error handling
async function handleDeleteCookies(sendResponse) {
  try {
    const result = await deleteCookiesForRecaptcha();
    safelySendResponse(sendResponse, result);
  } catch (error) {
    console.error('[reCAPTCHA Audio Solver] Error in handleDeleteCookies:', error);
    safelySendResponse(sendResponse, {
      success: false,
      error: error.message || 'Unknown error'
    });
  }
}

// Handle audio processing request with validation and retry logic
async function handleProcessAudio(request, sendResponse) {
  // Validate required parameters
  if (!request.audioUrl || !request.serverUrl) {
    safelySendResponse(sendResponse, {
      success: false,
      error: 'Missing required parameters: audioUrl and serverUrl'
    });
    return;
  }

  // Validate URL formats
  try {
    new URL(request.audioUrl);
    new URL(request.serverUrl);
  } catch (error) {
    safelySendResponse(sendResponse, {
      success: false,
      error: 'Invalid URL format'
    });
    return;
  }

  // Process the audio with retry logic
  let lastError = null;
  for (let attempt = 1; attempt <= CONFIG.MAX_RETRIES; attempt++) {
    try {
      const result = await processAudioFromServer(request.audioUrl, request.serverUrl);

      if (result.success) {
        safelySendResponse(sendResponse, result);
        return;
      }

      lastError = result.error;

      // Don't retry on certain errors
      if (result.status === 404 || result.status === 403) {
        break;
      }

      // Wait before retry (exponential backoff)
      if (attempt < CONFIG.MAX_RETRIES) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }

    } catch (error) {
      lastError = error.message;
      console.error(`[reCAPTCHA Audio Solver] Attempt ${attempt} failed:`, error.message);

      if (attempt < CONFIG.MAX_RETRIES) {
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  // All attempts failed
  safelySendResponse(sendResponse, {
    success: false,
    error: lastError || 'All retry attempts failed'
  });
}
