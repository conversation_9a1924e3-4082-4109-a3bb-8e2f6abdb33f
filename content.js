'use strict';

/**
 * reCAPTCHA Audio Solver v3.6.1 - Optimized
 * Enhanced version with improved performance and reliability
 */

(function () {
  'use strict';

  try {
    // Check if extension context is valid at startup
    if (!chrome || !chrome.runtime) {
      console.warn('[reCAPTCHA Audio Solver] Chrome runtime not available');
      return;
    }

    try {
      // Test extension context validity
      const testId = chrome.runtime.id;
      if (!testId) {
        console.warn('[reCAPTCHA Audio Solver] Extension context invalid at startup');
        return;
      }
    } catch (error) {
      console.warn('[reCAPTCHA Audio Solver] Extension context error at startup:', error.message);
      return;
    }

  // Enhanced Configuration
  const CONFIG = {
    VERSION: 'v3.6.1',
    MAX_ATTEMPTS_PER_SERVER: 3,
    MAX_TOTAL_ATTEMPTS: 5,
    POLLING_INTERVAL: 600, // Reduced from 400ms for better performance
    RETRY_DELAY: 1000, // Delay between retries
    AUDIO_TIMEOUT: 30000, // 30 seconds timeout for audio processing

    SELECTORS: {
      CHECKBOX: '.recaptcha-checkbox-border',
      AUDIO_BUTTON: '#recaptcha-audio-button',
      IMAGE_BUTTON: '#recaptcha-image-button',
      IMAGE_SELECT: '#rc-imageselect',
      AUDIO_SOURCE: '#audio-source',
      RESPONSE_FIELD: '.rc-audiochallenge-response-field',
      AUDIO_RESPONSE: '#audio-response',
      AUDIO_ERROR_MESSAGE: ".rc-audiochallenge-error-message",
      RELOAD_BUTTON: "#recaptcha-reload-button",
      DOSCAPTCHA: ".rc-doscaptcha-body",
      VERIFY_BUTTON: "#recaptcha-verify-button",
    },

    SERVERS: [
      'https://engageub.pythonanywhere.com',
      'https://engageub1.pythonanywhere.com',
      'https://engageub2.pythonanywhere.com',
    ],

    // Response validation patterns
    INVALID_PATTERNS: [/<|>/, /script/i, /javascript/i],
    MIN_RESPONSE_LENGTH: 3,
    MAX_RESPONSE_LENGTH: 50,
  };

  // Enhanced state management with proper encapsulation
  const state = {
    solved: false,
    waiting: false,
    serverIdx: 0,
    serverTry: 0,
    totalAttempts: 0,
    audioUrl: "",
    enabled: true,
    solveCount: 0,
    failCount: 0,
    processingCookies: false,
    lastProcessTime: 0,
    intervalId: null,
    timeoutId: null,
    extensionValid: true, // Track extension context validity
    shutdownRequested: false, // Track if shutdown was requested
  };

  // Performance optimization: Cache DOM elements
  const domCache = new Map();
  let cacheExpiry = 0;
  const CACHE_DURATION = 5000; // 5 seconds cache

  // Enhanced helper functions with performance optimizations
  const log = (msg, showConsole = true) => {
    if (showConsole) {
      console.log(`[reCAPTCHA Audio Solver ${CONFIG.VERSION}] ${msg}`);
    }
    updateStatus(msg);
  };

  // Optimized DOM selector with caching and error handling
  const $ = (sel) => {
    try {
      // Check if extension context is still valid
      if (!state.extensionValid || state.shutdownRequested) {
        return null;
      }

      const now = Date.now();
      if (now > cacheExpiry) {
        domCache.clear();
        cacheExpiry = now + CACHE_DURATION;
      }

      if (domCache.has(sel)) {
        return domCache.get(sel);
      }

      const element = document.querySelector(sel);
      if (element) {
        domCache.set(sel, element);
      }
      return element;
    } catch (error) {
      // Handle any errors during DOM access
      if (handleExtensionContextError(error, 'DOM selector')) {
        return null;
      }
      console.warn('[reCAPTCHA Audio Solver] Error in DOM selector:', error.message);
      return null;
    }
  };

  const isHidden = (el) => !el || !el.offsetParent;

  // Check if extension context is still valid
  const isExtensionValid = () => {
    try {
      // Try to access chrome.runtime.id - this will throw if context is invalid
      if (!chrome || !chrome.runtime) return false;

      // Try to access the runtime ID - this is the most reliable test
      const id = chrome.runtime.id;
      return !!id;
    } catch (error) {
      // Any error accessing chrome.runtime indicates invalid context
      return false;
    }
  };

  // Global error handler for extension context invalidation
  const handleExtensionContextError = (error, source = 'unknown') => {
    if (error && error.message &&
        (error.message.includes('Extension context invalidated') ||
         error.message.includes('message port closed') ||
         error.message.includes('receiving end does not exist'))) {
      console.warn(`[reCAPTCHA Audio Solver] Extension context invalidated in ${source}`);
      state.extensionValid = false;
      state.shutdownRequested = true;
      cleanup();
      return true;
    }
    return false;
  };

  // Safe wrapper for chrome.runtime.sendMessage with context validation
  const safeSendMessage = (message, callback) => {
    if (!isExtensionValid()) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalidated, stopping operations');
      state.extensionValid = false;
      state.shutdownRequested = true;
      cleanup();
      return false;
    }

    try {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          const error = chrome.runtime.lastError.message;
          if (error.includes('Extension context invalidated') ||
              error.includes('message port closed') ||
              error.includes('receiving end does not exist')) {
            console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during message');
            state.extensionValid = false;
            state.shutdownRequested = true;
            cleanup();
            return;
          }
        }
        if (callback && typeof callback === 'function') {
          callback(response);
        }
      });
      return true;
    } catch (error) {
      console.warn('[reCAPTCHA Audio Solver] Error sending message:', error.message);
      state.extensionValid = false;
      state.shutdownRequested = true;
      cleanup();
      return false;
    }
  };

  // Enhanced utility function to validate response text
  const isValidResponse = (text) => {
    if (!text || typeof text !== 'string') return false;
    if (text.length === 0 || text.length > CONFIG.MAX_RESPONSE_LENGTH) return false;

    // Check for known invalid responses
    const invalidResponses = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    if (invalidResponses.includes(text)) return false;

    // For short responses (1-2 characters), only allow letters
    if (text.length < CONFIG.MIN_RESPONSE_LENGTH) {
      if (!/^[a-zA-Z]+$/.test(text)) return false;
    }

    return !CONFIG.INVALID_PATTERNS.some(pattern => pattern.test(text));
  };

  // Debounced function to prevent rapid successive calls
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // Enhanced status update with better error handling and performance
  function updateStatus(status) {
    if (!status || !state.extensionValid || state.shutdownRequested) return;

    const data = {
      solveCount: state.solveCount,
      failCount: state.failCount,
      lastStatus: status,
      lastUpdated: Date.now()
    };

    // Safe storage update with extension context validation
    try {
      if (chrome && chrome.storage && chrome.storage.local) {
        chrome.storage.local.set(data).then(() => {
          // Send message only if storage was successful and extension is valid
          if (state.extensionValid && !state.shutdownRequested) {
            safeSendMessage({
              action: 'updateStatus',
              status,
              stats: {
                solved: state.solveCount,
                failed: state.failCount,
                enabled: state.enabled
              }
            });
          }
        }).catch(() => {
          // Silently handle storage errors
        });
      }
    } catch (error) {
      // Fallback for environments without Promise support
      try {
        if (chrome && chrome.storage && chrome.storage.local) {
          chrome.storage.local.set(data, () => {
            if (!chrome.runtime.lastError && state.extensionValid && !state.shutdownRequested) {
              safeSendMessage({
                action: 'updateStatus',
                status,
                stats: {
                  solved: state.solveCount,
                  failed: state.failCount,
                  enabled: state.enabled
                }
              });
            }
          });
        }
      } catch (e) {
        // Extension context is invalid, stop operations
        state.extensionValid = false;
        state.shutdownRequested = true;
        cleanup();
      }
    }
  }

  // Enhanced audio processing with better error handling and timeout
  function getTextFromAudio(url) {
    if (state.waiting || state.solved || !state.extensionValid || state.shutdownRequested) return;

    state.waiting = true;
    state.serverTry++;
    state.totalAttempts++;
    state.lastProcessTime = Date.now();

    const serverUrl = CONFIG.SERVERS[state.serverIdx];
    const processedUrl = url.replace("recaptcha.net", "google.com");

    log(`Kirim audio ke server ${state.serverIdx + 1}/${CONFIG.SERVERS.length} (Percobaan ${state.serverTry}, Total ${state.totalAttempts})`, true);

    // Set timeout for audio processing
    const timeoutId = setTimeout(() => {
      if (state.waiting) {
        state.waiting = false;
        log("Audio processing timeout", false);
        retryAudio();
      }
    }, CONFIG.AUDIO_TIMEOUT);

    // Use safe message sending
    const messageSent = safeSendMessage({
      action: 'processAudio',
      audioUrl: processedUrl,
      serverUrl
    }, response => {
      clearTimeout(timeoutId);
      state.waiting = false;

      // Early returns for error conditions
      if (state.solved || !state.extensionValid || state.shutdownRequested) return;
      if (!response || !response.success || !response.text) return retryAudio();

        const text = response.text.trim();

        // Enhanced validation using the utility function
        if (!isValidResponse(text)) {
          log(`Response rejected: "${text}" (length: ${text.length})`, true);
          return retryAudio();
        }

        // Get all required elements at once with fresh queries
        domCache.clear(); // Clear cache to get fresh elements
        const src = $(CONFIG.SELECTORS.AUDIO_SOURCE);
        const resp = $(CONFIG.SELECTORS.AUDIO_RESPONSE);
        const verify = $(CONFIG.SELECTORS.VERIFY_BUTTON);

        // Verify all elements exist and are in expected state
        if (!src || src.src !== state.audioUrl || !resp || resp.value || !verify) {
          return retryAudio();
        }

        // Fill in the response and click verify
        resp.value = text;

        // Small delay before clicking to ensure value is set
        setTimeout(() => {
          verify.click();

          // Update stats and status
          state.solveCount++;
          state.solved = true;
          log("Audio terisi & verifikasi diklik.", true);

          // Update storage with new stats
          chrome.storage.local.set({ solveCount: state.solveCount }).catch(() => {
            // Silently handle storage errors
          });
        }, 100);
      });

    // If message sending failed, cleanup and retry
    if (!messageSent) {
      clearTimeout(timeoutId);
      state.waiting = false;
      if (state.extensionValid && !state.shutdownRequested) {
        retryAudio();
      }
    }
  }

  // Enhanced retry logic with better state management and delays
  function retryAudio() {
    state.waiting = false;
    if (state.solved) return;

    // Fallback to image after max total attempts
    if (state.totalAttempts >= CONFIG.MAX_TOTAL_ATTEMPTS) {
      fallbackToImage("Max attempts reached, switching to image");
      return;
    }

    if (state.serverTry < CONFIG.MAX_ATTEMPTS_PER_SERVER) {
      log('Coba lagi server sama.', false);
      // Add delay before retry to prevent overwhelming the server
      setTimeout(() => {
        if (!state.solved) {
          getTextFromAudio(state.audioUrl);
        }
      }, CONFIG.RETRY_DELAY);
    } else {
      state.serverTry = 0;
      state.serverIdx = (state.serverIdx + 1) % CONFIG.SERVERS.length;

      if (state.serverIdx === 0) {
        // All servers failed, fallback to image
        fallbackToImage("Semua server gagal, beralih ke gambar");
      } else {
        log('Coba server berikutnya.', false);
        // Add delay before trying next server
        setTimeout(() => {
          if (!state.solved) {
            getTextFromAudio(state.audioUrl);
          }
        }, CONFIG.RETRY_DELAY);
      }
    }
  }

  // Helper function for fallback to image
  function fallbackToImage(message) {
    const imgBtn = $(CONFIG.SELECTORS.IMAGE_BUTTON);
    if (imgBtn && !isHidden(imgBtn)) {
      imgBtn.click();
    }

    state.solved = true;
    state.failCount++;

    // Update storage asynchronously
    chrome.storage.local.set({ failCount: state.failCount }).catch(() => {
      // Silently handle storage errors
    });

    log(message, true);
    resetCounters();
  }

  // Helper function to reset counters
  function resetCounters() {
    state.totalAttempts = 0;
    state.serverTry = 0;
    state.serverIdx = 0;
  }

  // Enhanced initialization with better error handling
  function initializeSolver() {
    // Check extension context validity before initialization
    if (!isExtensionValid()) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalid during initialization');
      state.extensionValid = false;
      state.shutdownRequested = true;
      return;
    }

    try {
      chrome.storage.local.get(['solveCount', 'failCount', 'enabled']).then((result) => {
        if (state.shutdownRequested || !state.extensionValid) return;

        state.solveCount = result.solveCount || 0;
        state.failCount = result.failCount || 0;
        state.enabled = result.enabled !== undefined ? result.enabled : true;

        // Update status to indicate the solver is active/inactive
        log(state.enabled ? "Aktif" : "Nonaktif", false);

        // Start the main solver
        startSolver();
      }).catch(() => {
        // Fallback for environments without Promise support
        if (state.shutdownRequested || !state.extensionValid) return;

        try {
          chrome.storage.local.get(['solveCount', 'failCount', 'enabled'], (result) => {
            if (chrome.runtime.lastError) {
              console.warn('Failed to load settings, using defaults');
              state.enabled = true;
            } else {
              state.solveCount = result.solveCount || 0;
              state.failCount = result.failCount || 0;
              state.enabled = result.enabled !== undefined ? result.enabled : true;
            }

            if (!state.shutdownRequested && state.extensionValid) {
              log(state.enabled ? "Aktif" : "Nonaktif", false);
              startSolver();
            }
          });
        } catch (error) {
          console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during fallback initialization');
          state.extensionValid = false;
          state.shutdownRequested = true;
        }
      });
    } catch (error) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during initialization');
      state.extensionValid = false;
      state.shutdownRequested = true;
    }
  }

  // Enhanced message handling with better error handling and validation
  chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (!request || typeof request !== 'object') {
      sendResponse({ success: false, error: 'Invalid request' });
      return false;
    }

    try {
      switch (request.action) {
        case "toggleSolver":
          if (typeof request.enabled !== 'boolean') {
            sendResponse({ success: false, error: 'Invalid enabled value' });
            return false;
          }

          state.enabled = request.enabled;
          chrome.storage.local.set({ enabled: state.enabled }).then(() => {
            sendResponse({ success: true, solverRunning: state.enabled });
          }).catch(() => {
            // Fallback
            chrome.storage.local.set({ enabled: state.enabled });
            sendResponse({ success: true, solverRunning: state.enabled });
          });
          return true;

        case "getStats":
          sendResponse({
            solved: state.solveCount,
            failed: state.failCount,
            enabled: state.enabled,
            status: state.waiting ? 'processing' : (state.solved ? 'success' : 'waiting'),
            captchaDetected: !!$(CONFIG.SELECTORS.CHECKBOX) || !!$(CONFIG.SELECTORS.AUDIO_BUTTON)
          });
          return true;

        case "resetBotDetection":
          // Reset the solved state so the solver can try again
          state.solved = false;
          resetCounters();

          // Delete cookies for recaptcha.net domain
          log("Menghapus cookies untuk domain recaptcha.net...", false);

          if (state.extensionValid && !state.shutdownRequested) {
            safeSendMessage({
              action: "deleteCookies"
            }, (response) => {
              if (response && response.success) {
                log(`Berhasil menghapus ${response.count} cookies`, false);
              } else {
                log("Gagal menghapus cookies", true);
              }

              // Update status
              log(state.enabled ? "Aktif" : "Nonaktif", false);
            });
          }

          sendResponse({ success: true });
          return true;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
          return false;
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
      return false;
    }
  });

  // Periodic health check to detect extension context invalidation
  function startHealthCheck() {
    // Check extension context every 5 seconds
    setInterval(() => {
      if (!state.shutdownRequested && state.extensionValid) {
        if (!isExtensionValid()) {
          console.warn('[reCAPTCHA Audio Solver] Extension context invalidated detected by health check');
          state.extensionValid = false;
          state.shutdownRequested = true;
          cleanup();
        }
      }
    }, 5000);
  }

  // Enhanced main solver function with better performance and error handling
  function startSolver() {
    // Clear any existing interval to prevent duplicates
    if (state.intervalId) {
      clearInterval(state.intervalId);
    }

    // Start health check
    startHealthCheck();

    state.intervalId = setInterval(() => {
      try {
        // Early returns for conditions that prevent processing
        if (!state.enabled || state.solved || state.processingCookies ||
            !state.extensionValid || state.shutdownRequested) {
          return;
        }

        // Check extension context validity periodically
        if (!isExtensionValid()) {
          console.warn('[reCAPTCHA Audio Solver] Extension context invalidated, stopping solver');
          state.extensionValid = false;
          state.shutdownRequested = true;
          cleanup();
          return;
        }

        // Throttle processing to prevent excessive DOM queries
        const now = Date.now();
        if (now - state.lastProcessTime < 200) return;
        state.lastProcessTime = now;

        try {
          // Check for bot detection
          const dos = $(CONFIG.SELECTORS.DOSCAPTCHA);
          if (dos && dos.innerText) {
            handleBotDetection();
            return;
          }

          // Process captcha elements
          processCaptchaElements();

        } catch (innerError) {
          // Handle extension context errors
          if (handleExtensionContextError(innerError, 'solver loop inner')) {
            return;
          }

          console.error('Error in solver loop inner:', innerError);
          // Continue processing despite other errors
        }

      } catch (outerError) {
        // Handle extension context errors at the outer level
        if (handleExtensionContextError(outerError, 'solver loop outer')) {
          return;
        }

        console.error('Error in solver loop outer:', outerError);
        // Continue processing despite other errors
      }
    }, CONFIG.POLLING_INTERVAL);
  }

  // Handle bot detection with proper state management
  function handleBotDetection() {
    const botMessage = "Bot terdeteksi! Coba lagi nanti.";
    log(botMessage, true);

    state.solved = true;
    state.failCount++;

    // Update storage with new stats and bot detection status
    const botData = {
      failCount: state.failCount,
      botDetected: true,
      lastStatus: botMessage
    };

    chrome.storage.local.set(botData).catch(() => {
      // Fallback for environments without Promise support
      chrome.storage.local.set(botData);
    });

    // Delete cookies for recaptcha.net domain
    if (state.extensionValid && !state.shutdownRequested) {
      safeSendMessage({
        action: "deleteCookies"
      });
    }

    // Send status update to popup
    updateStatus(botMessage);
  }

  // Process captcha elements with improved logic
  function processCaptchaElements() {
    // Click checkbox if not checked and not solved
    const checkbox = $(CONFIG.SELECTORS.CHECKBOX);
    if (checkbox && !isHidden(checkbox)) {
      handleCheckboxClick(checkbox);
      return;
    }

    // Click audio button if available
    const audioBtn = $(CONFIG.SELECTORS.AUDIO_BUTTON);
    const imgSel = $(CONFIG.SELECTORS.IMAGE_SELECT);
    if (audioBtn && !isHidden(audioBtn) && imgSel && !isHidden(imgSel)) {
      handleAudioButtonClick(audioBtn);
      return;
    }

    // Process audio if available
    processAudioChallenge();
  }

  // Handle checkbox click with cookie deletion
  function handleCheckboxClick(checkbox) {
    // Reset state for new captcha
    state.solved = false;
    state.audioUrl = "";
    resetCounters();

    // Set processing flag to prevent other operations
    state.processingCookies = true;

    // Delete cookies for recaptcha.net domain before clicking checkbox
    log("Menghapus cookies untuk domain recaptcha.net sebelum mengklik checkbox...", false);

    if (!state.extensionValid || state.shutdownRequested) {
      state.processingCookies = false;
      return;
    }

    const messageSent = safeSendMessage({
      action: "deleteCookies"
    }, (response) => {
      // Log result based on response
      if (response && response.success) {
        log(`Berhasil menghapus ${response.count} cookies`, false);
      } else {
        log("Gagal menghapus cookies", true);
      }

      // Click checkbox after cookies are deleted
      log("Mengklik checkbox setelah menghapus cookies", false);

      // Use a small delay to ensure the page is ready
      setTimeout(() => {
        if (checkbox && !isHidden(checkbox) && state.extensionValid && !state.shutdownRequested) {
          checkbox.click();
        }
        state.processingCookies = false;
      }, 100);
    });

    // If message sending failed, reset processing state
    if (!messageSent) {
      state.processingCookies = false;
    }
  }

  // Handle audio button click
  function handleAudioButtonClick(audioBtn) {
    // Reset attempt counters when switching to audio
    resetCounters();
    audioBtn.click();
    log("Tombol audio diklik.", false);
  }

  // Process audio challenge with enhanced logic
  function processAudioChallenge() {
    // Get all elements needed for audio processing at once
    const src = $(CONFIG.SELECTORS.AUDIO_SOURCE);
    const reload = $(CONFIG.SELECTORS.RELOAD_BUTTON);
    const errMsg = $(CONFIG.SELECTORS.AUDIO_ERROR_MESSAGE);
    const respField = $(CONFIG.SELECTORS.RESPONSE_FIELD);
    const resp = $(CONFIG.SELECTORS.AUDIO_RESPONSE);

    // Only process if we have an audio source
    if (!src || !src.src) return;

    // Handle audio errors and reload
    const needsReload = (!state.waiting && state.audioUrl === src.src && reload) ||
                       (errMsg && errMsg.innerText && reload && !reload.disabled);

    if (needsReload && reload && !reload.disabled) {
      reload.click();
      log("Reload audio.", false);
      state.audioUrl = ""; // reset audioUrl on reload
      return;
    }

    // Process new audio - enhanced condition check
    const shouldProcessAudio = !state.waiting && !state.solved &&
                              respField && !isHidden(respField) &&
                              (!resp || !resp.value) &&
                              state.audioUrl !== src.src;

    if (shouldProcessAudio) {
      state.audioUrl = src.src;
      log(`Audio baru: ${state.audioUrl}`, false);
      getTextFromAudio(state.audioUrl);
    }
  }

  // Enhanced cleanup function to prevent memory leaks and handle extension invalidation
  function cleanup() {
    console.log('[reCAPTCHA Audio Solver] Cleaning up resources...');

    // Set shutdown flag
    state.shutdownRequested = true;
    state.waiting = false;
    state.processingCookies = false;

    // Clear intervals and timeouts
    if (state.intervalId) {
      clearInterval(state.intervalId);
      state.intervalId = null;
    }
    if (state.timeoutId) {
      clearTimeout(state.timeoutId);
      state.timeoutId = null;
    }

    // Clear DOM cache
    domCache.clear();

    // Remove event listeners to prevent memory leaks
    try {
      window.removeEventListener('beforeunload', cleanup);
      window.removeEventListener('unload', cleanup);
    } catch (error) {
      // Silently ignore errors during cleanup
    }
  }

  // Handle page unload to cleanup resources
  window.addEventListener('beforeunload', cleanup);
  window.addEventListener('unload', cleanup);

  // Global error handler for extension context invalidation
  window.addEventListener('error', (event) => {
    if (event.error) {
      handleExtensionContextError(event.error, 'global error handler');
    }
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason) {
      handleExtensionContextError(event.reason, 'unhandled promise rejection');
    }
  });

  // Initialize the solver with a small delay to ensure DOM is ready
  setTimeout(() => {
    if (isExtensionValid()) {
      initializeSolver();
    } else {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalid at startup');
    }
  }, 100);

  } catch (globalError) {
    console.error('[reCAPTCHA Audio Solver] Global error during initialization:', globalError);
    // Try to handle extension context errors even during initialization
    if (globalError.message && globalError.message.includes('Extension context invalidated')) {
      console.warn('[reCAPTCHA Audio Solver] Extension context invalidated during initialization');
    }
  }
})();
