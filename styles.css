/* Enhanced CSS Variables with better organization and performance */
:root {
  /* Color Palette */
  --primary-blue: #4285F4;
  --primary-green: #34A853;
  --primary-yellow: #FBBC05;
  --primary-red: #EA4335;

  /* Light Theme Colors */
  --bg-color: #f5f5f5;
  --container-bg: #ffffff;
  --text-color: #333333;
  --text-secondary: #5f6368;
  --border-color: #e8eaed;
  --status-container-bg: #f8f9fa;
  --bottom-container-bg: #f8f9fa;

  /* Component Colors */
  --toggle-bg: rgba(0, 0, 0, 0.2);
  --toggle-handle: white;
  --lang-btn-bg: #ffffff;
  --lang-btn-color: #5f6368;
  --lang-btn-border: #e0e0e0;
  --lang-btn-active-bg: #e8f0fe;
  --lang-btn-active-color: var(--primary-blue);
  --lang-btn-active-border: var(--primary-blue);

  /* Gradients */
  --stat-success-bg: linear-gradient(135deg, #ffffff, #e8f5e9);
  --stat-fail-bg: linear-gradient(135deg, #ffffff, #ffebee);

  /* Shadows */
  --shadow-color: rgba(0, 0, 0, 0.12);
  --shadow-small: rgba(0, 0, 0, 0.08);
  --shadow-medium: rgba(0, 0, 0, 0.16);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 20px;
  --radius-full: 50%;
}

[data-theme="dark"] {
  /* Dark Theme Colors */
  --bg-color: #202124;
  --container-bg: #292a2d;
  --text-color: #e8eaed;
  --text-secondary: #9aa0a6;
  --border-color: #3c4043;
  --status-container-bg: #202124;
  --bottom-container-bg: #202124;

  /* Component Colors - Dark */
  --toggle-bg: rgba(255, 255, 255, 0.2);
  --toggle-handle: #e8eaed;
  --lang-btn-bg: #3c4043;
  --lang-btn-color: #e8eaed;
  --lang-btn-border: #5f6368;
  --lang-btn-active-bg: #174ea6;
  --lang-btn-active-color: #e8eaed;
  --lang-btn-active-border: #8ab4f8;

  /* Gradients - Dark */
  --stat-success-bg: linear-gradient(135deg, #292a2d, #0d3320);
  --stat-fail-bg: linear-gradient(135deg, #292a2d, #3d1c1c);

  /* Shadows - Dark */
  --shadow-color: rgba(0, 0, 0, 0.3);
  --shadow-small: rgba(0, 0, 0, 0.2);
  --shadow-medium: rgba(0, 0, 0, 0.4);
}

/* Enhanced base styles with better performance */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Product Sans', 'Segoe UI', system-ui, -apple-system, sans-serif;
}

/* Optimize font loading */
@font-face {
  font-family: 'Product Sans';
  font-display: swap;
}

body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-normal), color var(--transition-normal);
  font-size: 14px;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  width: 320px;
  padding: 0 0 var(--spacing-xs) 0;
  background-color: var(--container-bg);
  box-shadow: 0 3px 12px var(--shadow-color);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
  animation: container-appear 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* Optimized animation with better easing */
@keyframes container-appear {
  0% {
    opacity: 0;
    transform: translateY(8px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced header with better spacing and performance */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  position: relative;
  background: var(--container-bg);
  margin-bottom: 0;
  overflow: hidden;
  transition: background-color var(--transition-normal);
}

/* Optimized bottom container */
.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color var(--transition-normal), border-color var(--transition-normal);
}

/* Enhanced language selector with better performance */
.lang-selector {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
  z-index: 10;
}

.lang-btn {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-full);
  border: 2px solid var(--lang-btn-border);
  background-color: var(--lang-btn-bg);
  color: var(--lang-btn-color);
  font-weight: 600;
  font-size: 13px;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-shadow: 0 1px 3px var(--shadow-small);
  position: relative;
  overflow: hidden;
  will-change: transform;
}

/* Optimized hover effect */
.lang-btn::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: width var(--transition-normal), height var(--transition-normal);
  pointer-events: none;
}

.lang-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 5px var(--shadow-small);
}

.lang-btn:hover::before {
  width: 100%;
  height: 100%;
}

.lang-btn:active {
  transform: scale(0.95);
  transition: transform var(--transition-fast);
}

.lang-btn.lang-active {
  border-color: var(--lang-btn-active-border);
  color: var(--lang-btn-active-color);
  background-color: var(--lang-btn-active-bg);
  box-shadow: 0 2px 5px var(--shadow-small);
  transform: scale(1.05);
}

/* Enhanced dark mode toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);
}

.theme-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .theme-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-secondary);
  transition: fill var(--transition-normal), transform var(--transition-fast);
}

.theme-toggle:hover .theme-icon {
  transform: scale(1.1);
}

.header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 25%,
    #34A853 25%,
    #34A853 50%,
    #FBBC05 50%,
    #FBBC05 75%,
    #EA4335 75%,
    #EA4335 100%
  );
}

.header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: repeating-linear-gradient(
    to right,
    #4285F4 0%,
    #4285F4 25%,
    #34A853 25%,
    #34A853 50%,
    #FBBC05 50%,
    #FBBC05 75%,
    #EA4335 75%,
    #EA4335 100%
  );
}

/* Enhanced typography */
h1 {
  font-size: 20px;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  text-align: center;
  font-weight: 500;
  transition: color var(--transition-normal);
  letter-spacing: -0.02em;
}

/* Optimized toggle container */
.toggle-container {
  display: flex;
  align-items: center;
  margin-top: var(--spacing-xs);
  background-color: var(--status-container-bg);
  padding: 6px var(--spacing-md);
  border-radius: var(--radius-xl);
  box-shadow: 0 1px 3px var(--shadow-small);
  transition: background-color var(--transition-normal), box-shadow var(--transition-normal);
}

#toggleStatus {
  margin-left: var(--spacing-md);
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
  transition: color var(--transition-normal);
}

/* Enhanced Toggle Switch with better performance */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
  position: absolute;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--toggle-bg);
  transition: background-color var(--transition-normal);
  box-shadow: inset 0 1px 3px var(--shadow-small);
  overflow: hidden;
  will-change: background-color;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: var(--toggle-handle);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  box-shadow: 0 1px 3px var(--shadow-small);
  z-index: 2;
  will-change: transform;
}

.slider:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  background: repeating-linear-gradient(
    to right,
    var(--primary-blue) 0%,
    var(--primary-blue) 33%,
    var(--primary-green) 33%,
    var(--primary-green) 67%,
    var(--primary-yellow) 67%,
    var(--primary-yellow) 100%
  );
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
  will-change: opacity;
}

input:checked + .slider {
  background-color: transparent;
}

input:checked + .slider:after {
  opacity: 1;
}

input:focus + .slider {
  box-shadow: 0 0 3px var(--primary-blue);
}

input:checked + .slider:before {
  transform: translateX(26px);
  box-shadow: 0 2px 5px var(--shadow-medium);
}

.slider.round {
  border-radius: 26px;
}

.slider.round:before {
  border-radius: var(--radius-full);
}

/* Status */
.status-container {
  display: flex;
  align-items: center;
  margin: 20px;
  padding: 12px 15px;
  background-color: var(--status-container-bg);
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--shadow-small);
  border-left: 4px solid #4285F4;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%);
  z-index: 0;
}

.status-container > * {
  position: relative;
  z-index: 1;
}

/* Status icons */
.status-container.active::after {
  content: "✓";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #34A853;
}

.status-container.inactive::after {
  content: "○";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #EA4335;
}

.status-container.processing::after {
  content: "⟳";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: #FBBC05;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Bot detection status styling */
.status-container.bot-detected {
  border-left: 4px solid #FF0000;
  background-color: rgba(255, 0, 0, 0.1);
  position: relative;
  padding-right: 40px;
}

.status-container.bot-detected::after {
  content: "⚠️";
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  animation: pulse-warning 2s infinite;
}

[data-theme="dark"] .status-container.bot-detected {
  background-color: rgba(255, 0, 0, 0.2);
}

@keyframes pulse-warning {
  0% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1);
  }
}

/* Retry button container */
.retry-button-container {
  display: flex;
  justify-content: center;
  margin: 0 0 15px 0;
}

/* Retry button for bot detection */
.retry-button {
  display: none;
  padding: 8px 16px;
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px var(--shadow-small);
  position: relative;
  overflow: hidden;
  align-items: center;
  justify-content: center;
}

.retry-icon {
  width: 18px;
  height: 18px;
  fill: white;
  margin-right: 6px;
  transition: transform 0.3s ease;
}

.retry-text {
  display: inline-block;
  vertical-align: middle;
}

.retry-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.retry-button:hover {
  background-color: #3367d6;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-small);
}

.retry-button:hover .retry-icon {
  transform: rotate(180deg);
}

.retry-button:hover::before {
  left: 100%;
}

.retry-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px var(--shadow-small);
}

.retry-button.visible {
  display: flex;
  animation: fade-in 0.5s ease;
}



@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

[data-theme="dark"] .retry-button {
  background-color: #4285F4;
  color: white;
}

[data-theme="dark"] .retry-button:hover {
  background-color: #5c9aff;
}

/* Loading state styling */
.status-container.loading {
  border-left: 4px solid #FBBC05;
  animation: pulse 1.5s infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-label {
  font-weight: 600;
  margin-right: 10px;
  color: #4285F4;
  transition: color 0.3s ease;
}

.status-value {
  color: var(--text-color);
  flex: 1;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* Stats */
.stats-container {
  display: flex;
  justify-content: space-between;
  margin: 0 20px 20px;
}

.stat {
  flex: 1;
  padding: 15px 10px;
  border-radius: 8px;
  text-align: center;
  margin: 0 5px;
  box-shadow: 0 1px 3px var(--shadow-small);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, transparent 0%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px var(--shadow-small);
}

.stat:hover::after {
  opacity: 1;
}

.stat:first-child {
  margin-left: 0;
  background: var(--stat-success-bg);
  border-bottom: 3px solid #34A853;
}

.stat:last-child {
  margin-right: 0;
  background: var(--stat-fail-bg);
  border-bottom: 3px solid #EA4335;
}

.stat-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 8px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.stat-value {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

#solveCount {
  color: #34A853;
  transition: color 0.3s ease;
}

[data-theme="dark"] #solveCount {
  color: #4AE371; /* Brighter green for dark mode */
}

#failCount {
  color: #EA4335;
  transition: color 0.3s ease;
}

[data-theme="dark"] #failCount {
  color: #FF6B5E; /* Brighter red for dark mode */
}

/* Footer */
.footer {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
  padding: 12px 20px;
  background-color: var(--bottom-container-bg);
  border-top: 1px solid var(--border-color);
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.version {
  font-weight: 500;
}

.author {
  font-weight: 500;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.author-name {
  font-weight: 700;
  color: #4285F4;
  font-size: 14px;
  letter-spacing: 1px;
  text-transform: uppercase;
  position: relative;
  padding: 0 2px;
  text-shadow: 1px 1px 1px var(--shadow-small);
  transition: all 0.3s ease;
}

[data-theme="dark"] .author-name {
  color: #5C9CFF; /* Brighter blue for dark mode */
}

.author-name:hover {
  color: #EA4335;
  transform: scale(1.05);
}

/* Auto Handle Container */
