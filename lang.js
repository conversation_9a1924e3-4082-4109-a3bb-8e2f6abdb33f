'use strict';

// Language translations for reCAPTCHA Audio Solver
const translations = {
  en: {
    // Popup UI
    title: "reCAPTCHA Audio Solver",
    toggleActive: "Active",
    toggleInactive: "Inactive",
    statusLabel: "Status:",
    loading: "Loading...",
    successLabel: "Success:",
    failLabel: "Failed:",
    by: "by",

    // Status messages
    active: "Active",
    inactive: "Inactive",
    processing: "Processing...",
    success: "Success",
    successAlready: "Success (already solved)",
    waiting: "Waiting for captcha",
    error: "Error",
    botDetected: "Bot detected! Try again later",
    fallbackToImage: "Max attempts reached (5), switching to image",
    tryAgain: "Try Again",

    // Time formatting
    justNow: "Just now",
    secondsAgo: "seconds ago",
    minutesAgo: "minutes ago",
    noData: "No data",

    // Settings
    settings: "Settings",
    darkMode: "Dark Mode",
    language: "Language",
    server: "Server"
  },
  id: {
    // Popup UI
    title: "reCAPTCHA Audio Solver",
    toggleActive: "Aktif",
    toggleInactive: "Nonaktif",
    statusLabel: "Status:",
    loading: "Memuat...",
    successLabel: "Berhasil:",
    failLabel: "Gagal:",
    by: "by",

    // Status messages
    active: "Aktif",
    inactive: "Nonaktif",
    processing: "Memproses...",
    success: "Berhasil",
    successAlready: "Berhasil (sudah terselesaikan)",
    waiting: "Menunggu captcha",
    error: "Error",
    botDetected: "Bot terdeteksi! Coba lagi nanti",
    fallbackToImage: "Batas percobaan tercapai (5x), beralih ke gambar",
    tryAgain: "Coba Lagi",

    // Time formatting
    justNow: "Baru saja",
    secondsAgo: "detik yang lalu",
    minutesAgo: "menit yang lalu",
    noData: "Tidak ada data",

    // Settings
    settings: "Pengaturan",
    darkMode: "Mode Gelap",
    language: "Bahasa",
    server: "Server"
  },
  es: {
    // Popup UI
    title: "reCAPTCHA Audio Solver",
    toggleActive: "Activo",
    toggleInactive: "Inactivo",
    statusLabel: "Estado:",
    loading: "Cargando...",
    successLabel: "Éxito:",
    failLabel: "Fallido:",
    by: "por",

    // Status messages
    active: "Activo",
    inactive: "Inactivo",
    processing: "Procesando...",
    success: "Éxito",
    successAlready: "Éxito (ya resuelto)",
    waiting: "Esperando captcha",
    error: "Error",
    botDetected: "¡Bot detectado! Inténtalo más tarde",
    fallbackToImage: "Límite de intentos alcanzado (5), cambiando a imagen",
    tryAgain: "Intentar de nuevo",

    // Time formatting
    justNow: "Ahora mismo",
    secondsAgo: "segundos atrás",
    minutesAgo: "minutos atrás",
    noData: "Sin datos",

    // Settings
    settings: "Configuración",
    darkMode: "Modo Oscuro",
    language: "Idioma",
    server: "Servidor"
  }
};

// Default language
let currentLang = 'id'; // Default to Indonesian

// Function to get translation - optimized
function t(key) {
  // Use optional chaining and nullish coalescing for cleaner fallback logic
  return translations[currentLang]?.[key] ||
         translations.en?.[key] ||
         translations.id?.[key] ||
         key;
}

// Function to set language - optimized
function setLanguage(lang) {
  // Early return if language is invalid
  if (!translations[lang]) return false;

  // Set the current language
  currentLang = lang;

  // Save language preference to localStorage with error handling
  try {
    localStorage.setItem('reCAPTCHA_solver_lang', lang);
  } catch (e) {
    // Silently ignore storage errors
    console.debug('Failed to save language preference:', e);
  }

  return true;
}

// Function to get current language - unchanged
function getLanguage() {
  return currentLang;
}

// Initialize language from localStorage - optimized
function initLanguage() {
  try {
    const storedLang = localStorage.getItem('reCAPTCHA_solver_lang');
    // Only update if valid language found
    if (storedLang && translations[storedLang]) {
      currentLang = storedLang;
    }
  } catch (e) {
    console.debug('Failed to load language preference:', e);
  }
}

// Initialize language when script loads
initLanguage();
