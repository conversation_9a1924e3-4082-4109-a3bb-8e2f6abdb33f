
# 🎧 reCAPTCHA Audio Solver

[![Versi](https://img.shields.io/badge/versi-3.6.0-blue.svg)](https://github.com/Moryata/Recaptcha-Audio)
[![Lisensi](https://img.shields.io/badge/lisensi-Open%20Source-green.svg)](LICENSE.md)

**Otomatis selesaikan reCAPTCHA audio di Chrome dengan UI modern dan multi-bahasa!**

## ✨ Fitur Utama

- 🤖 **Otomatis**: Deteksi, klik, proses audio, dan isi jawaban secara otomatis
- 🌙 **Mode Gelap**: Tampilan nyaman untuk penggunaan malam hari
- 🈳 **Multi-Bahasa**: Dukungan bahasa EN/ID/ES
- 📊 **Statistik**: Pantau jumlah captcha berhasil/gagal secara real-time
- 🛡️ **Privasi**: Tidak mengumpulkan data pribadi, komunikasi terenkripsi

## 🚀 Instalasi Cepat

1. **Clone/unduh** repo ini
   ```bash
   git clone https://github.com/moryata/Recaptcha-Audio.git
   ```
2. Buka Chrome → `chrome://extensions/` → Aktifkan **Developer mode**
3. Klik **Load unpacked** → Pilih folder hasil clone
4. Ekstensi siap digunakan!

## 💡 Cara Pakai

1. Klik ikon ekstensi di toolbar Chrome
2. Aktifkan toggle untuk mulai otomatisasi
3. Pilih bahasa dan mode tampilan sesuai preferensi
4. Buka halaman dengan reCAPTCHA
5. Ekstensi akan otomatis menyelesaikan captcha audio

## ⚠️ Troubleshooting

- **Bot terdeteksi**: Klik tombol "Coba Lagi" atau tunggu beberapa saat
- **Audio gagal diproses**: Server mungkin sibuk, coba lagi nanti
- **Solver tidak berjalan**: Pastikan toggle aktif dan refresh halaman

## 🔄 Versi Terbaru (3.6.0)

- 🔄 **Fallback Otomatis**: Beralih ke verifikasi gambar setelah 5x percobaan audio gagal
- ⚡ **Performa Ditingkatkan**: Kode dioptimalkan untuk kecepatan dan efisiensi lebih baik
- 🔄 **Penghapusan Cookie Paralel**: Proses penghapusan cookie lebih cepat dengan Promise.all
- 🛡️ **Error Handling Lebih Baik**: Penanganan error yang lebih robust di semua komponen
- 🎨 **UI Modern**: Desain baru dengan animasi dan efek visual yang lebih baik
- ⚠️ **Deteksi Bot**: Tampilan pesan bot detection dengan tombol "Coba Lagi"
- ✨ **Animasi Status**: Ikon dan animasi untuk setiap kondisi (aktif/nonaktif)
- 📱 **Responsif**: Tampilan optimal di berbagai ukuran layar

## 👨‍💻 Kredit & Lisensi

Dikembangkan oleh **Muhammad Surya Pratama (Moryata)**
Open source, silakan gunakan sesuai lisensi di LICENSE.md