<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>reCAPTCHA Audio Solver</title>
  <link rel="stylesheet" href="styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Product+Sans:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 id="title">reCAPTCHA Audio Solver</h1>
      <div class="toggle-container">
        <label class="switch">
          <input type="checkbox" id="solverToggle">
          <span class="slider round"></span>
        </label>
        <span id="toggleStatus">Nonaktif</span>
      </div>
    </div>

    <div class="status-container">
      <div id="statusLabel" class="status-label">Status:</div>
      <div id="status" class="status-value">Memuat...</div>
    </div>

    <div class="retry-button-container">
      <button id="retryButton" class="retry-button">
        <svg class="retry-icon" viewBox="0 0 24 24">
          <path d="M17.65,6.35C16.2,4.9 14.21,4 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20C15.73,20 18.84,17.45 19.73,14H17.65C16.83,16.33 14.61,18 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6C13.66,6 15.14,6.69 16.22,7.78L13,11H20V4L17.65,6.35Z" />
        </svg>
        <span class="retry-text"></span>
      </button>
    </div>

    <div class="stats-container">
      <div class="stat">
        <div id="successLabel" class="stat-label">Berhasil:</div>
        <div id="solveCount" class="stat-value">0</div>
      </div>
      <div class="stat">
        <div id="failLabel" class="stat-label">Gagal:</div>
        <div id="failCount" class="stat-value">0</div>
      </div>
    </div>


    <div class="footer">
      <div class="version">v3.6.1</div>
      <div class="author">by <span class="author-name">⭐ Moryata ⭐</span></div>
    </div>

    <div class="bottom-container">
      <div class="lang-selector">
        <button id="langEN" class="lang-btn">EN</button>
        <button id="langID" class="lang-btn lang-active">ID</button>
        <button id="langES" class="lang-btn">ES</button>
      </div>
      <div class="theme-toggle" id="themeToggle">
        <svg class="theme-icon" id="themeIcon" viewBox="0 0 24 24">
          <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />
        </svg>
      </div>
    </div>
  </div>
  <script src="lang.js"></script>
  <script src="popup.js"></script>
</body>
</html>
