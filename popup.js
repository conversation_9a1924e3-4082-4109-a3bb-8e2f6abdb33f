'use strict';

/**
 * reCAPTCHA Audio Solver v3.6.1 - Enhanced
 * Popup script with improved performance and user experience
 */

// Configuration constants
const POPUP_CONFIG = {
  VERSION: 'v3.6.1',
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 150,
  UPDATE_THROTTLE: 100,
  MAX_ANIMATION_VALUE: 999
};

// Performance optimization: Cache DOM elements
const elements = {};
let isInitialized = false;

// Enhanced time formatting with better performance
function formatLastUpdated(timestamp) {
  if (!timestamp) return t('noData');

  const now = Date.now();
  const diff = now - timestamp;

  // Optimized time calculations with constants
  const SECOND = 1000;
  const MINUTE = 60 * SECOND;
  const HOUR = 60 * MINUTE;

  if (diff < SECOND) return t('justNow');
  if (diff < MINUTE) return `${Math.floor(diff / SECOND)} ${t('secondsAgo')}`;
  if (diff < HOUR) return `${Math.floor(diff / MINUTE)} ${t('minutesAgo')}`;

  return new Date(timestamp).toLocaleTimeString();
}

// Enhanced value animation with better performance and memory management
function animateValue(element, start, end, duration = POPUP_CONFIG.ANIMATION_DURATION) {
  // Validate inputs
  if (!element || typeof start !== 'number' || typeof end !== 'number') {
    if (element) element.textContent = end || 0;
    return;
  }

  // Skip animation for very small changes or if values are the same
  const diff = Math.abs(end - start);
  if (diff < 2) {
    element.textContent = end;
    return;
  }

  // Limit maximum values for performance
  const clampedEnd = Math.min(end, POPUP_CONFIG.MAX_ANIMATION_VALUE);
  const clampedStart = Math.min(start, POPUP_CONFIG.MAX_ANIMATION_VALUE);

  // Adjust duration based on difference for better UX
  const adjustedDuration = Math.min(duration, Math.max(200, diff * 50));

  // Cancel any existing animation on this element
  if (element._animationId) {
    cancelAnimationFrame(element._animationId);
  }

  const startTime = performance.now();
  let lastValue = clampedStart;

  const animate = (currentTime) => {
    const elapsedTime = currentTime - startTime;
    const progress = Math.min(elapsedTime / adjustedDuration, 1);

    // Use easeOutCubic for more natural animation
    const easedProgress = 1 - Math.pow(1 - progress, 3);
    const currentValue = Math.round(easedProgress * (clampedEnd - clampedStart) + clampedStart);

    // Only update DOM if value changed (performance optimization)
    if (currentValue !== lastValue) {
      element.textContent = currentValue;
      lastValue = currentValue;
    }

    if (progress < 1) {
      element._animationId = requestAnimationFrame(animate);
    } else {
      element._animationId = null;
      element.textContent = clampedEnd; // Ensure final value is exact
    }
  };

  element._animationId = requestAnimationFrame(animate);
}

// Enhanced status animation with better performance and accessibility
function updateStatusWithAnimation(element, text) {
  // Validate inputs
  if (!element || typeof text !== 'string') return;

  // Skip animation if text hasn't changed
  if (element.textContent === text) return;

  // Cancel any existing animation
  if (element._statusAnimationTimeout) {
    clearTimeout(element._statusAnimationTimeout);
  }

  // Use CSS transitions for better performance
  element.style.transition = `opacity ${POPUP_CONFIG.DEBOUNCE_DELAY}ms ease-in-out`;
  element.style.opacity = '0';

  element._statusAnimationTimeout = setTimeout(() => {
    element.textContent = text;

    // Use requestAnimationFrame for smooth transition
    requestAnimationFrame(() => {
      element.style.opacity = '1';

      // Clean up timeout reference
      element._statusAnimationTimeout = null;
    });
  }, POPUP_CONFIG.DEBOUNCE_DELAY);
}

// Enhanced UI language update with better performance and caching
function updateUILanguage() {
  // Cache elements if not already cached
  if (!elements.title) {
    elements.title = document.getElementById('title');
    elements.statusLabel = document.getElementById('statusLabel');
    elements.successLabel = document.getElementById('successLabel');
    elements.failLabel = document.getElementById('failLabel');
    elements.toggleStatus = document.getElementById('toggleStatus');
    elements.status = document.getElementById('status');
    elements.solverToggle = document.getElementById('solverToggle');
  }

  // Define element-to-translation mapping
  const translations = [
    [elements.title, 'title'],
    [elements.statusLabel, 'statusLabel'],
    [elements.successLabel, 'successLabel'],
    [elements.failLabel, 'failLabel']
  ];

  // Update all text elements efficiently
  translations.forEach(([element, key]) => {
    if (element) element.textContent = t(key);
  });

  // Handle toggle status with state dependency
  if (elements.toggleStatus && elements.solverToggle) {
    elements.toggleStatus.textContent = elements.solverToggle.checked ?
      t('toggleActive') : t('toggleInactive');
  }

  // Update status text with pattern matching
  if (elements.status) {
    updateStatusTranslation(elements.status);
  }
}

// Separate function for status translation with optimized pattern matching
function updateStatusTranslation(statusElement) {
  const currentStatus = statusElement.textContent;
  if (!currentStatus) return;

  // Optimized status mapping with regex for better performance
  const statusPatterns = [
    { regex: /^(Aktif|Active|Activo)$/i, key: 'active' },
    { regex: /^(Nonaktif|Inactive|Inactivo)$/i, key: 'inactive' },
    { regex: /^(Memproses|Processing|Procesando)/i, key: 'processing' },
    { regex: /^(Memuat|Loading|Cargando)/i, key: 'loading' },
    { regex: /(Bot terdeteksi|Bot detected|Bot detectado)/i, key: 'botDetected' }
  ];

  // Find matching pattern and update
  for (const { regex, key } of statusPatterns) {
    if (regex.test(currentStatus)) {
      statusElement.textContent = t(key);
      return;
    }
  }
}

// Enhanced dark mode toggle with better performance and error handling
function toggleDarkMode() {
  // Cache icon paths as constants for better performance
  const ICON_PATHS = Object.freeze({
    light: '<path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z" />',
    dark: '<path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />'
  });

  try {
    const isDarkMode = document.body.getAttribute('data-theme') === 'dark';
    const newMode = isDarkMode ? 'light' : 'dark';

    // Update theme attribute efficiently
    if (isDarkMode) {
      document.body.removeAttribute('data-theme');
    } else {
      document.body.setAttribute('data-theme', 'dark');
    }

    // Save preference with error handling
    try {
      localStorage.setItem('reCAPTCHA_solver_theme', newMode);
    } catch (storageError) {
      console.warn('Failed to save theme preference:', storageError.message);
      // Continue with theme change even if storage fails
    }

    // Update icon efficiently
    if (!elements.themeIcon) {
      elements.themeIcon = document.getElementById('themeIcon');
    }

    if (elements.themeIcon) {
      elements.themeIcon.innerHTML = ICON_PATHS[newMode];
    }

    // Dispatch custom event for other components that might need to know about theme change
    document.dispatchEvent(new CustomEvent('themeChanged', {
      detail: { theme: newMode }
    }));

  } catch (error) {
    console.error('Error toggling dark mode:', error);
  }
}

// Enhanced DOM initialization with better performance and error handling
function initializeElements() {
  // Cache all DOM elements for better performance
  elements.solverToggle = document.getElementById('solverToggle');
  elements.toggleStatus = document.getElementById('toggleStatus');
  elements.statusElement = document.getElementById('status');
  elements.solveCountElement = document.getElementById('solveCount');
  elements.failCountElement = document.getElementById('failCount');
  elements.langEN = document.getElementById('langEN');
  elements.langID = document.getElementById('langID');
  elements.langES = document.getElementById('langES');
  elements.themeToggle = document.getElementById('themeToggle');
  elements.retryButton = document.getElementById('retryButton');
  elements.statusContainer = document.querySelector('.status-container');
  elements.retryText = document.querySelector('#retryButton .retry-text');

  // Validate critical elements
  const criticalElements = ['solverToggle', 'statusElement', 'solveCountElement', 'failCountElement'];
  const missingElements = criticalElements.filter(key => !elements[key]);

  if (missingElements.length > 0) {
    console.error('Missing critical elements:', missingElements);
    return false;
  }

  return true;
}

// Enhanced theme initialization with error handling
function initializeTheme() {
  try {
    const savedTheme = localStorage.getItem('reCAPTCHA_solver_theme');
    if (savedTheme === 'dark') {
      document.body.setAttribute('data-theme', 'dark');
      const themeIcon = document.getElementById('themeIcon');
      if (themeIcon) {
        themeIcon.innerHTML = '<path d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />';
      }
    }
  } catch (error) {
    console.warn('Failed to initialize theme:', error.message);
  }
}

// Enhanced language initialization
function initializeLanguage() {
  try {
    const currentLang = getLanguage();

    // Set language button states efficiently
    const langButtons = [
      [elements.langEN, 'en'],
      [elements.langID, 'id'],
      [elements.langES, 'es']
    ];

    langButtons.forEach(([button, lang]) => {
      if (button) {
        button.classList.toggle('lang-active', currentLang === lang);
      }
    });

    // Update UI with current language
    updateUILanguage();

    // Set retry button text
    if (elements.retryText) {
      elements.retryText.textContent = t('tryAgain');
    }
  } catch (error) {
    console.error('Failed to initialize language:', error);
  }
}

// Main initialization function
document.addEventListener('DOMContentLoaded', () => {
  try {
    // Initialize elements and validate
    if (!initializeElements()) {
      console.error('Failed to initialize critical elements');
      return;
    }

    // Mark as initialized
    isInitialized = true;

    // Add loading state
    if (elements.statusContainer) {
      elements.statusContainer.classList.add('loading');
    }

    // Initialize theme
    initializeTheme();

    // Add transition styles for smoother animations
    if (elements.statusElement) {
      elements.statusElement.style.transition = `opacity ${POPUP_CONFIG.DEBOUNCE_DELAY}ms ease`;
    }

    // Initialize language
    initializeLanguage();

    // Show initial loading status
    if (elements.statusElement) {
      updateStatusWithAnimation(elements.statusElement, t('loading'));
    }

    // Set up event listeners
    setupEventListeners();

    // Load initial data
    loadInitialData();

  } catch (error) {
    console.error('Failed to initialize popup:', error);
  }
});



// Enhanced event listeners setup with better error handling
function setupEventListeners() {
  // Theme toggle event listener
  if (elements.themeToggle) {
    elements.themeToggle.addEventListener('click', toggleDarkMode);
  }

  // Retry button event listener with enhanced functionality
  if (elements.retryButton) {
    elements.retryButton.addEventListener('click', handleRetryClick);
  }

  // Language switching event listeners
  setupLanguageListeners();

  // Solver toggle event listener
  if (elements.solverToggle) {
    elements.solverToggle.addEventListener('change', handleSolverToggle);
  }

  // Storage change listener
  chrome.storage.onChanged.addListener(handleStorageChange);
}

// Enhanced retry button handler
function handleRetryClick() {
  try {
    // Reset bot detection status
    chrome.storage.local.set({ botDetected: false }).catch(() => {
      // Fallback for environments without Promise support
      chrome.storage.local.set({ botDetected: false });
    });

    // Update UI
    if (elements.statusElement && elements.solverToggle) {
      updateStatusWithAnimation(
        elements.statusElement,
        elements.solverToggle.checked ? t('active') : t('inactive')
      );
    }

    // Update status container
    if (elements.statusContainer && elements.solverToggle) {
      elements.statusContainer.classList.remove('bot-detected');
      elements.statusContainer.style.borderLeftColor =
        elements.solverToggle.checked ? '#4285F4' : '#EA4335';
    }

    // Hide retry button
    if (elements.retryButton) {
      elements.retryButton.classList.remove('visible');
    }

    // Notify all tabs with reCAPTCHA to reset
    notifyTabsToReset();

  } catch (error) {
    console.error('Error handling retry click:', error);
  }
}

// Enhanced tab notification with better error handling
function notifyTabsToReset() {
  const urlPatterns = [
    "*://*/recaptcha/*",
    "*://*.google.com/recaptcha/*",
    "*://*.recaptcha.net/recaptcha/*"
  ];

  try {
    chrome.tabs.query({ url: urlPatterns }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.warn('Failed to query tabs:', chrome.runtime.lastError.message);
        return;
      }

      tabs.forEach(tab => {
        try {
          chrome.tabs.sendMessage(tab.id, {
            action: "resetBotDetection"
          }, (response) => {
            // Handle response or errors silently
            if (chrome.runtime.lastError) {
              // Expected for tabs that don't have the content script
            }
          });
        } catch (error) {
          // Silently ignore individual tab errors
        }
      });
    });
  } catch (error) {
    console.error('Error notifying tabs:', error);
  }
}

// Enhanced language switching with better performance
function setupLanguageListeners() {
  const languageButtons = [
    { element: elements.langEN, lang: 'en', others: [elements.langID, elements.langES] },
    { element: elements.langID, lang: 'id', others: [elements.langEN, elements.langES] },
    { element: elements.langES, lang: 'es', others: [elements.langEN, elements.langID] }
  ];

  languageButtons.forEach(({ element, lang, others }) => {
    if (element) {
      element.addEventListener('click', () => handleLanguageSwitch(lang, element, others));
    }
  });
}

// Optimized language switch handler with debouncing
const handleLanguageSwitch = debounce((lang, button, otherButtons) => {
  try {
    if (getLanguage() !== lang) {
      setLanguage(lang);

      // Update active state for all buttons efficiently
      button.classList.add('lang-active');
      otherButtons.forEach(btn => {
        if (btn) btn.classList.remove('lang-active');
      });

      // Update UI with new language
      updateUILanguage();
    }
  } catch (error) {
    console.error('Error switching language:', error);
  }
}, POPUP_CONFIG.DEBOUNCE_DELAY);

// Debounce utility function for better performance
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Enhanced data loading with better error handling and performance
function loadInitialData() {
  try {
    chrome.storage.local.get(['enabled', 'solveCount', 'failCount', 'botDetected', 'lastStatus'])
      .then(handleInitialData)
      .catch(() => {
        // Fallback for environments without Promise support
        chrome.storage.local.get(['enabled', 'solveCount', 'failCount', 'botDetected', 'lastStatus'], handleInitialData);
      });
  } catch (error) {
    console.error('Error loading initial data:', error);
    // Set default values
    handleInitialData({});
  }
}

// Handle initial data with enhanced UI updates
function handleInitialData(data) {
  try {
    // Set default values if not found
    const isEnabled = data.enabled !== undefined ? data.enabled : true;
    const solveCount = data.solveCount || 0;
    const failCount = data.failCount || 0;
    const botDetected = data.botDetected || false;

    // Update solver toggle and status
    if (elements.solverToggle && elements.toggleStatus) {
      elements.solverToggle.checked = isEnabled;
      elements.toggleStatus.textContent = isEnabled ? t('toggleActive') : t('toggleInactive');
    }

    // Update status based on bot detection
    if (botDetected || (data.lastStatus && data.lastStatus.includes('Bot terdeteksi'))) {
      handleBotDetectedState();
    } else {
      handleNormalState(isEnabled);
    }

    // Update counters with animation
    if (elements.solveCountElement && elements.failCountElement) {
      animateValue(elements.solveCountElement, 0, solveCount, 600);
      animateValue(elements.failCountElement, 0, failCount, 600);
    }

  } catch (error) {
    console.error('Error handling initial data:', error);
  }
}

// Handle bot detected state
function handleBotDetectedState() {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, t('botDetected'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading', 'active', 'inactive', 'processing');
    elements.statusContainer.classList.add('bot-detected');
    elements.statusContainer.style.borderLeftColor = '#FF0000';
  }

  if (elements.retryButton) {
    elements.retryButton.classList.add('visible');
  }

  if (elements.retryText) {
    elements.retryText.textContent = t('tryAgain');
  }
}

// Handle normal state
function handleNormalState(isEnabled) {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, isEnabled ? t('active') : t('inactive'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading', 'bot-detected', 'processing');

    if (isEnabled) {
      elements.statusContainer.classList.add('active');
      elements.statusContainer.classList.remove('inactive');
      elements.statusContainer.style.borderLeftColor = '#4285F4';
    } else {
      elements.statusContainer.classList.add('inactive');
      elements.statusContainer.classList.remove('active');
      elements.statusContainer.style.borderLeftColor = '#EA4335';
    }
  }

  if (elements.retryButton) {
    elements.retryButton.classList.remove('visible');
  }
}

// Enhanced solver toggle handler with better performance and error handling
function handleSolverToggle() {
  try {
    const enabled = elements.solverToggle.checked;

    // Update toggle status text
    if (elements.toggleStatus) {
      elements.toggleStatus.textContent = enabled ? t('toggleActive') : t('toggleInactive');
    }

    // Reset bot detection status when toggling
    chrome.storage.local.get(['botDetected'])
      .then(data => handleToggleWithBotCheck(enabled, data))
      .catch(() => {
        // Fallback for environments without Promise support
        chrome.storage.local.get(['botDetected'], data => handleToggleWithBotCheck(enabled, data));
      });

  } catch (error) {
    console.error('Error handling solver toggle:', error);
  }
}

// Handle toggle with bot detection check
function handleToggleWithBotCheck(enabled, data) {
  try {
    const wasBotDetected = data.botDetected || false;

    // If bot was detected, reset the status
    if (wasBotDetected) {
      chrome.storage.local.set({ botDetected: false }).catch(() => {
        chrome.storage.local.set({ botDetected: false });
      });
    }

    // Update status with animation
    if (elements.statusElement) {
      updateStatusWithAnimation(elements.statusElement, enabled ? t('active') : t('inactive'));
    }

    // Update status container
    updateStatusContainer(enabled);

    // Save state to storage
    chrome.storage.local.set({ enabled }).catch(() => {
      chrome.storage.local.set({ enabled });
    });

    // Notify tabs about the toggle
    notifyTabsAboutToggle(enabled);

  } catch (error) {
    console.error('Error in handleToggleWithBotCheck:', error);
  }
}

// Update status container efficiently
function updateStatusContainer(enabled) {
  if (!elements.statusContainer) return;

  try {
    elements.statusContainer.classList.remove('bot-detected', 'loading', 'processing');

    if (enabled) {
      elements.statusContainer.classList.add('active');
      elements.statusContainer.classList.remove('inactive');
      elements.statusContainer.style.borderLeftColor = '#4285F4';
    } else {
      elements.statusContainer.classList.add('inactive');
      elements.statusContainer.classList.remove('active');
      elements.statusContainer.style.borderLeftColor = '#EA4335';
    }
  } catch (error) {
    console.error('Error updating status container:', error);
  }
}

// Notify tabs about solver toggle
function notifyTabsAboutToggle(enabled) {
  const urlPatterns = [
    "*://*/recaptcha/*",
    "*://*.google.com/recaptcha/*",
    "*://*.recaptcha.net/recaptcha/*"
  ];

  try {
    chrome.tabs.query({ url: urlPatterns }, (tabs) => {
      if (chrome.runtime.lastError) {
        console.warn('Failed to query tabs for toggle:', chrome.runtime.lastError.message);
        return;
      }

      tabs.forEach(tab => {
        try {
          chrome.tabs.sendMessage(tab.id, {
            action: "toggleSolver",
            enabled
          }, () => {
            // Handle response or errors silently
            if (chrome.runtime.lastError) {
              // Expected for tabs that don't have the content script
            }
          });
        } catch (error) {
          // Silently ignore individual tab errors
        }
      });
    });
  } catch (error) {
    console.error('Error notifying tabs about toggle:', error);
  }
}

// Enhanced storage change handler with better performance and error handling
function handleStorageChange(changes, namespace) {
  if (namespace !== 'local' || !isInitialized) return;

  try {
    // Handle counter updates with throttling
    if (changes.solveCount && elements.solveCountElement) {
      const currentValue = parseInt(elements.solveCountElement.textContent) || 0;
      animateValue(elements.solveCountElement, currentValue, changes.solveCount.newValue, 400);
    }

    if (changes.failCount && elements.failCountElement) {
      const currentValue = parseInt(elements.failCountElement.textContent) || 0;
      animateValue(elements.failCountElement, currentValue, changes.failCount.newValue, 400);
    }

    // Handle bot detection status changes
    if (changes.botDetected) {
      if (changes.botDetected.newValue === true) {
        handleBotDetectedChange();
      } else if (changes.botDetected.newValue === false && elements.retryButton) {
        elements.retryButton.classList.remove('visible');
      }
    }

    // Handle lastStatus changes for bot detection
    if (changes.lastStatus && changes.lastStatus.newValue &&
        changes.lastStatus.newValue.includes('Bot terdeteksi')) {
      handleBotDetectedChange();
    }

    // Handle enabled state changes
    if (changes.enabled && elements.solverToggle &&
        elements.solverToggle.checked !== changes.enabled.newValue) {
      handleEnabledStateChange(changes.enabled.newValue);
    }

  } catch (error) {
    console.error('Error handling storage change:', error);
  }
}

// Handle bot detected change
function handleBotDetectedChange() {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, t('botDetected'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading');
    elements.statusContainer.classList.add('bot-detected');
    elements.statusContainer.style.borderLeftColor = '#FF0000';
  }

  if (elements.retryButton) {
    elements.retryButton.classList.add('visible');
  }

  if (elements.retryText) {
    elements.retryText.textContent = t('tryAgain');
  }
}

// Handle enabled state change
function handleEnabledStateChange(newEnabledValue) {
  try {
    if (elements.solverToggle && elements.toggleStatus) {
      elements.solverToggle.checked = newEnabledValue;
      elements.toggleStatus.textContent = newEnabledValue ? t('toggleActive') : t('toggleInactive');
    }

    // Only update status if not in bot detected state
    chrome.storage.local.get(['botDetected'])
      .then(data => {
        if (!data.botDetected) {
          updateStatusForEnabledChange(newEnabledValue);
        }
      })
      .catch(() => {
        // Fallback
        chrome.storage.local.get(['botDetected'], (data) => {
          if (!chrome.runtime.lastError && !data.botDetected) {
            updateStatusForEnabledChange(newEnabledValue);
          }
        });
      });

  } catch (error) {
    console.error('Error handling enabled state change:', error);
  }
}

// Update status for enabled change
function updateStatusForEnabledChange(enabled) {
  if (elements.statusElement) {
    updateStatusWithAnimation(elements.statusElement, enabled ? t('active') : t('inactive'));
  }

  if (elements.statusContainer) {
    elements.statusContainer.classList.remove('loading', 'bot-detected', 'processing');

    if (enabled) {
      elements.statusContainer.classList.add('active');
      elements.statusContainer.classList.remove('inactive');
      elements.statusContainer.style.borderLeftColor = '#4285F4';
    } else {
      elements.statusContainer.classList.add('inactive');
      elements.statusContainer.classList.remove('active');
      elements.statusContainer.style.borderLeftColor = '#EA4335';
    }
  }
}
